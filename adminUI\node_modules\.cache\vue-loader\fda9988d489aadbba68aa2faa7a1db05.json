{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\maintain\\devconfig\\combineDataList.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\maintain\\devconfig\\combineDataList.vue", "mtime": 1754620576859}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport edit from './combineEdit'\r\nimport * as systemGroupDataApi from '@/api/systemGroupData.js'\r\nimport * as systemFormConfigApi from '@/api/systemFormConfig.js'\r\nexport default {\r\n  // name: \"combineDataList\"\r\n  components: { edit },\r\n  props: {\r\n    formData: {\r\n      type: Object,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      constants: this.$constants,\r\n      listPram: {\r\n        gid: null,\r\n        keywords: null,\r\n        status: null, // 1=开启 2=关闭\r\n        page: 1,\r\n        pageSize: this.$constants.page.limit[0]\r\n      },\r\n      editDataConfig: {\r\n        visible: false,\r\n        isCreate: 0, // 0=create 1=edit\r\n        editData: {}\r\n      },\r\n      formConf: { fields: [] },\r\n      dataList: { list: [], total: 0 },\r\n      formMark:0\r\n    }\r\n  },\r\n  mounted() {\r\n    this.handlerGetFormConfig()\r\n    this.listPram.gid = this.formData.id\r\n    this.handlerGetListData(this.listPram)\r\n  },\r\n  methods: {\r\n    handlerSearch() {\r\n      this.listPram.page = 1\r\n      this.handlerGetListData(this.listPram)\r\n    },\r\n    handlerGetListData(pram) { // 获取列表数据\r\n      systemGroupDataApi.groupDataList(pram).then(data => {\r\n        const _selfList = []\r\n        data.list.forEach(_lItem => {\r\n          _lItem.value = JSON.parse(_lItem.value)\r\n          const _fields = _lItem.value.fields\r\n          const _rowData = {}\r\n          _fields.map((item) => {\r\n            _rowData[item.name] = item.value\r\n          })\r\n          _rowData.id = _lItem.id\r\n          _rowData.sort = _lItem.sort\r\n          _rowData.status = _lItem.status\r\n          _selfList.push(_rowData)\r\n        })\r\n        this.dataList.list = _selfList\r\n        this.dataList.total = data.total\r\n      })\r\n    },\r\n    handlerGetFormConfig() { // 获取表单配置后生成table列\r\n      const _pram = { id: this.formData.formId }\r\n      systemFormConfigApi.getFormConfigInfo(_pram).then(data => {\r\n        this.formMark = parseInt(data.id);\r\n        this.formConf = JSON.parse(data.content)\r\n      })\r\n    },\r\n    handlerOpenEditData(rowData, isCreate) {\r\n      this.editDataConfig.editData = rowData\r\n      this.editDataConfig.isCreate = isCreate\r\n      this.editDataConfig.visible = true\r\n    },\r\n    handlerHideDia() {\r\n      this.handlerGetListData(this.listPram)\r\n      this.editDataConfig.visible = false\r\n    },\r\n    handlerDelete(rowData) {\r\n      this.$confirm('确实删除当前数据', '提示').then(() => {\r\n        systemGroupDataApi.groupDataDelete(rowData).then(data => {\r\n          this.$message.success('删除数据成功')\r\n          this.handlerHideDia()\r\n        })\r\n      })\r\n    },\r\n    handleSizeChange(val) {\r\n      this.listPram.limit = val\r\n      this.handlerGetListData(this.listPram)\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.listPram.page = val\r\n      this.handlerGetListData(this.listPram)\r\n    }\r\n  }\r\n}\r\n", null]}