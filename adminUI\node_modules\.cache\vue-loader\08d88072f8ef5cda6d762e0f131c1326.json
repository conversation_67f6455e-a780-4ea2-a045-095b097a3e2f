{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\Tinymce\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\Tinymce\\index.vue", "mtime": 1754622102723}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport editorImage from './components/EditorImage'\r\nimport plugins from './plugins'\r\nimport toolbar from './toolbar'\r\n\r\nexport default {\r\n  name: 'Tinymce',\r\n  components: { editorImage },\r\n  props: {\r\n    id: {\r\n      type: String,\r\n      default: function() {\r\n        return 'vue-tinymce-' + +new Date() + ((Math.random() * 1000).toFixed(0) + '')\r\n      }\r\n    },\r\n    value: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    toolbar: {\r\n      type: Array,\r\n      required: false,\r\n      default() {\r\n        return []\r\n      }\r\n    },\r\n    menubar: {\r\n      type: String,\r\n      default: 'file edit insert view format table'\r\n    },\r\n    height: {\r\n      type: Number,\r\n      required: false,\r\n      default: 400\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      hasChange: false,\r\n      hasInit: false,\r\n      tinymceId: this.id,\r\n      fullscreen: false,\r\n      languageTypeList: {\r\n        'en': 'en',\r\n        'zh-CN': 'zh_CN',\r\n        'id': 'id'\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    language() {\r\n      // 获取当前语言设置\r\n      const currentLocale = this.$i18n.locale || localStorage.getItem('locale') || 'zh-CN'\r\n      return this.languageTypeList[currentLocale] || 'zh_CN'\r\n    }\r\n  },\r\n  watch: {\r\n    value(val) {\r\n      if (!this.hasChange && this.hasInit) {\r\n        this.$nextTick(() =>\r\n          window.tinymce.get(this.tinymceId).setContent(val || ''))\r\n      }\r\n    },\r\n    language() {\r\n      this.destroyTinymce()\r\n      this.$nextTick(() => this.initTinymce())\r\n    },\r\n    // 监听 i18n 语言变化\r\n    '$i18n.locale'() {\r\n      this.destroyTinymce()\r\n      this.$nextTick(() => this.initTinymce())\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initTinymce()\r\n  },\r\n  activated() {\r\n    this.initTinymce()\r\n  },\r\n  deactivated() {\r\n    this.destroyTinymce()\r\n  },\r\n  destroyed() {\r\n    this.destroyTinymce()\r\n  },\r\n  methods: {\r\n    initTinymce() {\r\n      const _this = this\r\n\r\n      // 动态加载语言包\r\n      this.loadLanguagePack().then(() => {\r\n        window.tinymce.init({\r\n          language: this.language,\r\n          selector: `#${this.tinymceId}`,\r\n          height: this.height,\r\n          body_class: 'panel-body ',\r\n          object_resizing: false,\r\n          toolbar: this.toolbar.length > 0 ? this.toolbar : toolbar,\r\n          menubar: this.menubar,\r\n          plugins: plugins,\r\n          end_container_on_empty_block: true,\r\n          powerpaste_word_import: 'clean',\r\n          code_dialog_height: 450,\r\n          code_dialog_width: 1000,\r\n          advlist_bullet_styles: 'square',\r\n          advlist_number_styles: 'default',\r\n          imagetools_cors_hosts: ['www.tinymce.com', 'codepen.io'],\r\n          default_link_target: '_blank',\r\n          link_title: false,\r\n          convert_urls: false, //防止路径被转化为相对路径\r\n          nonbreaking_force_tab: true, // inserting nonbreaking space &nbsp; need Nonbreaking Space Plugin\r\n          init_instance_callback: editor => {\r\n            if (_this.value) {\r\n              editor.setContent(_this.value)\r\n            }\r\n            _this.hasInit = true\r\n            editor.on('NodeChange Change KeyUp SetContent', () => {\r\n              this.hasChange = true\r\n              this.$emit('input', editor.getContent())\r\n            })\r\n          },\r\n          setup(editor) {\r\n            editor.on('FullscreenStateChanged', (e) => {\r\n              _this.fullscreen = e.state\r\n            })\r\n          }\r\n        })\r\n      })\r\n    },\r\n    loadLanguagePack() {\r\n      return new Promise((resolve) => {\r\n        // 如果是中文或者语言包已经加载，直接返回\r\n        if (this.language === 'zh_CN' || window.tinymce.i18n.data[this.language]) {\r\n          resolve()\r\n          return\r\n        }\r\n\r\n        // 动态加载语言包\r\n        const script = document.createElement('script')\r\n        script.src = `/static/tinymce4.7.5/langs/${this.language}.js`\r\n        script.onload = () => {\r\n          resolve()\r\n        }\r\n        script.onerror = () => {\r\n          console.warn(`Failed to load TinyMCE language pack: ${this.language}`)\r\n          resolve() // 即使加载失败也继续，使用默认语言\r\n        }\r\n        document.head.appendChild(script)\r\n      })\r\n    },\r\n    destroyTinymce() {\r\n      const tinymce = window.tinymce.get(this.tinymceId)\r\n      if (this.fullscreen) {\r\n        tinymce.execCommand('mceFullScreen')\r\n      }\r\n\r\n      if (tinymce) {\r\n        tinymce.destroy()\r\n      }\r\n    },\r\n    setContent(value) {\r\n      window.tinymce.get(this.tinymceId).setContent(value)\r\n    },\r\n    getContent() {\r\n      window.tinymce.get(this.tinymceId).getContent()\r\n    },\r\n    imageSuccessCBK(arr) {\r\n      const _this = this;\r\n      arr.forEach((v) => {\r\n        if (this.getFileType(v) == \"video\") {\r\n          window.tinymce\r\n            .get(_this.tinymceId)\r\n            .insertContent(\r\n              `<video class=\"wscnph\" src=\"${v}\" controls muted></video>`\r\n            );\r\n        } else {\r\n          window.tinymce\r\n            .get(_this.tinymceId)\r\n            .insertContent(`<img class=\"wscnph\" src=\"${v}\" />`);\r\n        }\r\n      });\r\n    },\r\n    getFileType(fileName) {\r\n      // 后缀获取\r\n      let suffix = \"\";\r\n      // 获取类型结果\r\n      let result = \"\";\r\n      try {\r\n        const flieArr = fileName.split(\".\");\r\n        suffix = flieArr[flieArr.length - 1];\r\n      } catch (err) {\r\n        suffix = \"\";\r\n      }\r\n      // fileName无后缀返回 false\r\n      if (!suffix) {\r\n        return false;\r\n      }\r\n      suffix = suffix.toLocaleLowerCase();\r\n      // 图片格式\r\n      const imglist = [\"png\", \"jpg\", \"jpeg\", \"bmp\", \"gif\"];\r\n      // 进行图片匹配\r\n      result = imglist.find((item) => item === suffix);\r\n      if (result) {\r\n        return \"image\";\r\n      }\r\n      // 匹配 视频\r\n      const videolist = [\r\n        \"mp4\",\r\n        \"m2v\",\r\n        \"mkv\",\r\n        \"rmvb\",\r\n        \"wmv\",\r\n        \"avi\",\r\n        \"flv\",\r\n        \"mov\",\r\n        \"m4v\",\r\n      ];\r\n      result = videolist.find((item) => item === suffix);\r\n      if (result) {\r\n        return \"video\";\r\n      }\r\n      // 其他 文件类型\r\n      return \"other\";\r\n    },\r\n  }\r\n}\r\n", null]}