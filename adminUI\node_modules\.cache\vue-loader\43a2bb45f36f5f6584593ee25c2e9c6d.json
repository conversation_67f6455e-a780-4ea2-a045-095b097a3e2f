{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\maintain\\devconfig\\combineDataList.vue?vue&type=template&id=34433436&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\maintain\\devconfig\\combineDataList.vue", "mtime": 1754620576859}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"components-container\" },\n    [\n      _c(\n        \"div\",\n        { staticClass: \"container\" },\n        [\n          _c(\n            \"el-form\",\n            { attrs: { inline: \"\" } },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { label: _vm.$t(\"common.status.status\") } },\n                [\n                  _c(\n                    \"el-select\",\n                    {\n                      staticClass: \"selWidth\",\n                      attrs: {\n                        placeholder: _vm.$t(\"common.status.status\"),\n                        clearable: \"\",\n                      },\n                      on: { change: _vm.handlerSearch },\n                      model: {\n                        value: _vm.listPram.status,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.listPram, \"status\", $$v)\n                        },\n                        expression: \"listPram.status\",\n                      },\n                    },\n                    _vm._l(_vm.statusOptions, function (item) {\n                      return _c(\"el-option\", {\n                        key: item.value,\n                        attrs: { label: item.label, value: item.value },\n                      })\n                    }),\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-button\",\n        {\n          directives: [\n            {\n              name: \"hasPermi\",\n              rawName: \"v-hasPermi\",\n              value: [\"admin:system:group:data:save\"],\n              expression: \"['admin:system:group:data:save']\",\n            },\n          ],\n          attrs: { type: \"primary\", size: \"mini\" },\n          on: {\n            click: function ($event) {\n              return _vm.handlerOpenEditData({}, 0)\n            },\n          },\n        },\n        [_vm._v(\"添加数据\")]\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: _vm.editDataConfig.isCreate === 0 ? \"添加数据\" : \"编辑数据\",\n            visible: _vm.editDataConfig.visible,\n            \"append-to-body\": \"\",\n            \"destroy-on-close\": \"\",\n            width: \"700px\",\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              return _vm.$set(_vm.editDataConfig, \"visible\", $event)\n            },\n          },\n        },\n        [\n          _vm.editDataConfig.visible\n            ? _c(\"edit\", {\n                attrs: {\n                  \"form-data\": _vm.formData,\n                  \"edit-data\": _vm.editDataConfig.editData,\n                  \"is-create\": _vm.editDataConfig.isCreate,\n                },\n                on: { hideDialog: _vm.handlerHideDia },\n              })\n            : _vm._e(),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-table\",\n        {\n          staticStyle: { width: \"100%\", \"margin-bottom\": \"20px\" },\n          attrs: {\n            data: _vm.dataList.list,\n            \"header-cell-style\": { fontWeight: \"bold\" },\n          },\n        },\n        [\n          _c(\"el-table-column\", { attrs: { label: \"编号\", prop: \"id\" } }),\n          _vm._v(\" \"),\n          _vm._l(_vm.formConf.fields, function (item, index) {\n            return _c(\"el-table-column\", {\n              key: index,\n              attrs: { label: item.__config__.label, prop: item.__vModel__ },\n              scopedSlots: _vm._u(\n                [\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        [\"img\", \"image\", \"pic\"].indexOf(item.__vModel__) > -1\n                          ? _c(\n                              \"div\",\n                              { staticClass: \"demo-image__preview\" },\n                              [\n                                _c(\"el-image\", {\n                                  staticStyle: {\n                                    width: \"36px\",\n                                    height: \"36px\",\n                                  },\n                                  attrs: {\n                                    src: scope.row[item.__vModel__],\n                                    \"preview-src-list\": [\n                                      scope.row[item.__vModel__],\n                                    ],\n                                  },\n                                }),\n                              ],\n                              1\n                            )\n                          : _c(\"span\", [\n                              _vm._v(_vm._s(scope.row[item.__vModel__])),\n                            ]),\n                      ]\n                    },\n                  },\n                ],\n                null,\n                true\n              ),\n            })\n          }),\n          _vm._v(\" \"),\n          _c(\"el-table-column\", {\n            attrs: { label: \"状态\", prop: \"status\" },\n            scopedSlots: _vm._u([\n              {\n                key: \"default\",\n                fn: function (scope) {\n                  return [\n                    _c(\"span\", [\n                      _vm._v(\n                        _vm._s(_vm._f(\"filterShowOrHide\")(scope.row.status))\n                      ),\n                    ]),\n                  ]\n                },\n              },\n            ]),\n          }),\n          _vm._v(\" \"),\n          _c(\"el-table-column\", {\n            attrs: { label: \"操作\", width: \"200\" },\n            scopedSlots: _vm._u([\n              {\n                key: \"default\",\n                fn: function (scope) {\n                  return [\n                    _c(\n                      \"el-button\",\n                      {\n                        directives: [\n                          {\n                            name: \"hasPermi\",\n                            rawName: \"v-hasPermi\",\n                            value: [\n                              \"admin:system:group:data:update\",\n                              \"admin:system:group:data:info\",\n                            ],\n                            expression:\n                              \"['admin:system:group:data:update','admin:system:group:data:info']\",\n                          },\n                        ],\n                        attrs: { type: \"text\", size: \"small\" },\n                        on: {\n                          click: function ($event) {\n                            return _vm.handlerOpenEditData(scope.row, 1)\n                          },\n                        },\n                      },\n                      [_vm._v(\"编辑\")]\n                    ),\n                    _vm._v(\" \"),\n                    _vm.formMark !== 99\n                      ? _c(\n                          \"el-button\",\n                          {\n                            directives: [\n                              {\n                                name: \"hasPermi\",\n                                rawName: \"v-hasPermi\",\n                                value: [\"admin:system:group:data:delete\"],\n                                expression:\n                                  \"['admin:system:group:data:delete']\",\n                              },\n                            ],\n                            attrs: { type: \"text\", size: \"small\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.handlerDelete(scope.row)\n                              },\n                            },\n                          },\n                          [_vm._v(\"删除\")]\n                        )\n                      : _vm._e(),\n                  ]\n                },\n              },\n            ]),\n          }),\n        ],\n        2\n      ),\n      _vm._v(\" \"),\n      _c(\"el-pagination\", {\n        attrs: {\n          \"current-page\": _vm.listPram.page,\n          \"page-sizes\": _vm.constants.page.limit,\n          layout: _vm.constants.page.layout,\n          total: _vm.dataList.total,\n        },\n        on: {\n          \"size-change\": _vm.handleSizeChange,\n          \"current-change\": _vm.handleCurrentChange,\n        },\n      }),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}