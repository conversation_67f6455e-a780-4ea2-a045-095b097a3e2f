{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\maintain\\devconfig\\combineDataList.vue?vue&type=template&id=34433436&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\maintain\\devconfig\\combineDataList.vue", "mtime": 1754620576859}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div class=\"components-container\">\n  <div class=\"container\">\n    <el-form inline>\n      <el-form-item :label=\"$t('common.status.status')\">\n        <el-select v-model=\"listPram.status\" :placeholder=\"$t('common.status.status')\" clearable @change=\"handlerSearch\" class=\"selWidth\">\n          <el-option\n            v-for=\"item in statusOptions\"\n            :key=\"item.value\"\n            :label=\"item.label\"\n            :value=\"item.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <!--        <el-form-item label=\"关键词\">-->\n      <!--          <el-input v-model=\"listPram.keywords\" placeholder=\"请输入关键词\" clearable></el-input>-->\n      <!--        </el-form-item>-->\n      <!--        <el-form-item>-->\n      <!--          <el-button type=\"primary\" @click=\"handlerSearch\">查询</el-button>-->\n      <!--        </el-form-item>-->\n    </el-form>\n  </div>\n  <el-button type=\"primary\" size=\"mini\" @click=\"handlerOpenEditData({},0)\" v-hasPermi=\"['admin:system:group:data:save']\">添加数据</el-button>\n  <!-- v-if=\"((formData.id==55 || formData.name==='签到天数配置') && dataList.list.length<7) || (formData.id!=55|| formData.name!=='签到天数配置')\" -->\n  <el-dialog\n    :title=\"editDataConfig.isCreate === 0?'添加数据':'编辑数据'\"\n    :visible.sync=\"editDataConfig.visible\"\n    append-to-body\n    destroy-on-close\n    width=\"700px\"\n  >\n    <edit\n      v-if=\"editDataConfig.visible\"\n      :form-data=\"formData\"\n      :edit-data=\"editDataConfig.editData\"\n      :is-create=\"editDataConfig.isCreate\"\n      @hideDialog=\"handlerHideDia\"\n    />\n  </el-dialog>\n  <el-table\n    :data=\"dataList.list\"\n    style=\"width: 100%;margin-bottom: 20px;\"\n    :header-cell-style=\" {fontWeight:'bold'}\"\n  >\n    <el-table-column label=\"编号\" prop=\"id\" />\n    <el-table-column\n      v-for=\"item,index in formConf.fields\"\n      :key=\"index\"\n      :label=\"item.__config__.label\"\n      :prop=\"item.__vModel__\"\n    >\n      <template slot-scope=\"scope\">\n        <div v-if=\"['img','image','pic'].indexOf(item.__vModel__) > -1\" class=\"demo-image__preview\">\n          <el-image\n            style=\"width: 36px; height: 36px\"\n            :src=\"scope.row[item.__vModel__]\"\n            :preview-src-list=\"[scope.row[item.__vModel__]]\"\n          />\n        </div>\n        <span v-else>{{ scope.row[item.__vModel__] }}</span>\n      </template>\n    </el-table-column>\n    <el-table-column label=\"状态\" prop=\"status\">\n      <template slot-scope=\"scope\">\n        <span>{{ scope.row.status | filterShowOrHide }}</span>\n      </template>\n    </el-table-column>\n    <el-table-column label=\"操作\" width=\"200\">\n      <template slot-scope=\"scope\">\n        <el-button type=\"text\" size=\"small\" @click=\"handlerOpenEditData(scope.row,1)\" v-hasPermi=\"['admin:system:group:data:update','admin:system:group:data:info']\">编辑</el-button>\n        <el-button type=\"text\" size=\"small\" @click=\"handlerDelete(scope.row)\" v-if=\"formMark !== 99\" v-hasPermi=\"['admin:system:group:data:delete']\">删除</el-button>\n      </template>\n    </el-table-column>\n  </el-table>\n  <el-pagination\n    :current-page=\"listPram.page\"\n    :page-sizes=\"constants.page.limit\"\n    :layout=\"constants.page.layout\"\n    :total=\"dataList.total\"\n    @size-change=\"handleSizeChange\"\n    @current-change=\"handleCurrentChange\"\n  />\n</div>\n", null]}