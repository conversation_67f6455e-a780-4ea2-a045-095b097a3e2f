{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\maintain\\devconfig\\combineDataList.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\maintain\\devconfig\\combineDataList.vue", "mtime": 1754620576859}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _combineEdit = _interopRequireDefault(require(\"./combineEdit\"));\nvar systemGroupDataApi = _interopRequireWildcard(require(\"@/api/systemGroupData.js\"));\nvar systemFormConfigApi = _interopRequireWildcard(require(\"@/api/systemFormConfig.js\"));\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  // name: \"combineDataList\"\n  components: {\n    edit: _combineEdit.default\n  },\n  props: {\n    formData: {\n      type: Object,\n      required: true\n    }\n  },\n  data: function data() {\n    return {\n      constants: this.$constants,\n      listPram: {\n        gid: null,\n        keywords: null,\n        status: null,\n        // 1=开启 2=关闭\n        page: 1,\n        pageSize: this.$constants.page.limit[0]\n      },\n      editDataConfig: {\n        visible: false,\n        isCreate: 0,\n        // 0=create 1=edit\n        editData: {}\n      },\n      formConf: {\n        fields: []\n      },\n      dataList: {\n        list: [],\n        total: 0\n      },\n      formMark: 0\n    };\n  },\n  mounted: function mounted() {\n    this.handlerGetFormConfig();\n    this.listPram.gid = this.formData.id;\n    this.handlerGetListData(this.listPram);\n  },\n  methods: {\n    handlerSearch: function handlerSearch() {\n      this.listPram.page = 1;\n      this.handlerGetListData(this.listPram);\n    },\n    handlerGetListData: function handlerGetListData(pram) {\n      var _this = this;\n      // 获取列表数据\n      systemGroupDataApi.groupDataList(pram).then(function (data) {\n        var _selfList = [];\n        data.list.forEach(function (_lItem) {\n          _lItem.value = JSON.parse(_lItem.value);\n          var _fields = _lItem.value.fields;\n          var _rowData = {};\n          _fields.map(function (item) {\n            _rowData[item.name] = item.value;\n          });\n          _rowData.id = _lItem.id;\n          _rowData.sort = _lItem.sort;\n          _rowData.status = _lItem.status;\n          _selfList.push(_rowData);\n        });\n        _this.dataList.list = _selfList;\n        _this.dataList.total = data.total;\n      });\n    },\n    handlerGetFormConfig: function handlerGetFormConfig() {\n      var _this2 = this;\n      // 获取表单配置后生成table列\n      var _pram = {\n        id: this.formData.formId\n      };\n      systemFormConfigApi.getFormConfigInfo(_pram).then(function (data) {\n        _this2.formMark = parseInt(data.id);\n        _this2.formConf = JSON.parse(data.content);\n      });\n    },\n    handlerOpenEditData: function handlerOpenEditData(rowData, isCreate) {\n      this.editDataConfig.editData = rowData;\n      this.editDataConfig.isCreate = isCreate;\n      this.editDataConfig.visible = true;\n    },\n    handlerHideDia: function handlerHideDia() {\n      this.handlerGetListData(this.listPram);\n      this.editDataConfig.visible = false;\n    },\n    handlerDelete: function handlerDelete(rowData) {\n      var _this3 = this;\n      this.$confirm('确实删除当前数据', '提示').then(function () {\n        systemGroupDataApi.groupDataDelete(rowData).then(function (data) {\n          _this3.$message.success('删除数据成功');\n          _this3.handlerHideDia();\n        });\n      });\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.listPram.limit = val;\n      this.handlerGetListData(this.listPram);\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.listPram.page = val;\n      this.handlerGetListData(this.listPram);\n    }\n  }\n};", null]}